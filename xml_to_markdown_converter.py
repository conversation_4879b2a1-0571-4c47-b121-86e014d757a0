#!/usr/bin/env python3
"""
XML to Markdown Converter

This script converts an XML file to a properly formatted Markdown file.
It handles various XML structures and creates readable markdown output.
"""

import xml.etree.ElementTree as ET
import re
import os
from pathlib import Path
from typing import Dict, List, Optional


class XMLToMarkdownConverter:
    """Converts XML content to Markdown format."""

    def __init__(self, use_tables: bool = True, max_heading_level: int = 6):
        self.markdown_content = []
        self.current_level = 0
        self.use_tables = use_tables
        self.max_heading_level = max_heading_level
        
    def clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize line breaks
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Escape markdown special characters if needed
        # text = re.sub(r'([*_`\[\]()#+\-.])', r'\\\1', text)
        
        return text
    
    def get_element_text(self, element: ET.Element) -> str:
        """Extract all text content from an element and its children."""
        text_parts = []
        
        if element.text:
            text_parts.append(element.text.strip())
        
        for child in element:
            child_text = self.get_element_text(child)
            if child_text:
                text_parts.append(child_text)
            if child.tail:
                text_parts.append(child.tail.strip())
        
        return ' '.join(text_parts)
    
    def format_attributes(self, element: ET.Element) -> str:
        """Format element attributes as markdown."""
        if not element.attrib:
            return ""
        
        attr_parts = []
        for key, value in element.attrib.items():
            attr_parts.append(f"**{key}**: {value}")
        
        return " (" + ", ".join(attr_parts) + ")"
    
    def process_element(self, element: ET.Element, level: int = 0) -> None:
        """Process an XML element and convert it to markdown."""
        tag_name = element.tag
        element_text = self.clean_text(element.text) if element.text else ""
        attributes = self.format_attributes(element)

        # Determine heading level (max specified for markdown)
        heading_level = min(level + 1, self.max_heading_level)
        heading_prefix = "#" * heading_level

        # Check if this element and its siblings could form a table
        if self.use_tables and level > 0:
            parent = element.getparent() if hasattr(element, 'getparent') else None
            if parent is not None:
                siblings = [child for child in parent if child.tag == element.tag]
                if len(siblings) > 1 and self.should_create_table(siblings):
                    # Skip individual processing, will be handled as table
                    return

        # Add the element as a heading
        if tag_name:
            heading = f"{heading_prefix} {tag_name.title()}{attributes}"
            self.markdown_content.append(heading)
            self.markdown_content.append("")  # Empty line after heading

        # Add element text content if present
        if element_text:
            self.markdown_content.append(element_text)
            self.markdown_content.append("")  # Empty line after content

        # Process child elements
        child_groups = self.group_similar_children(element)
        for tag, children in child_groups.items():
            if len(children) > 1 and self.use_tables and self.should_create_table(children):
                # Create table for similar elements
                self.markdown_content.append(f"### {tag.title()}")
                self.markdown_content.append("")
                table_lines = create_table_from_elements(children)
                self.markdown_content.extend(table_lines)
                self.markdown_content.append("")
            else:
                # Process individually
                for child in children:
                    self.process_element(child, level + 1)

        # Add tail text if present
        if element.tail and element.tail.strip():
            tail_text = self.clean_text(element.tail)
            self.markdown_content.append(tail_text)
            self.markdown_content.append("")

    def group_similar_children(self, element: ET.Element) -> Dict[str, List[ET.Element]]:
        """Group child elements by tag name."""
        groups = {}
        for child in element:
            tag = child.tag
            if tag not in groups:
                groups[tag] = []
            groups[tag].append(child)
        return groups

    def should_create_table(self, elements: List[ET.Element]) -> bool:
        """Determine if elements should be formatted as a table."""
        if len(elements) < 2:
            return False

        # Check if elements have similar structure (attributes or child elements)
        first_element = elements[0]
        first_keys = set(first_element.attrib.keys()) | set([child.tag for child in first_element])

        for element in elements[1:]:
            element_keys = set(element.attrib.keys()) | set([child.tag for child in element])
            # If there's some overlap in structure, consider it table-worthy
            if len(first_keys & element_keys) > 0:
                return True

        return False
    
    def process_list_elements(self, elements: List[ET.Element], parent_name: str) -> None:
        """Process elements that should be formatted as lists."""
        if not elements:
            return
            
        self.markdown_content.append(f"## {parent_name.title()}")
        self.markdown_content.append("")
        
        for i, element in enumerate(elements, 1):
            text_content = self.get_element_text(element)
            attributes = self.format_attributes(element)
            
            if text_content or attributes:
                self.markdown_content.append(f"{i}. **{element.tag}**{attributes}")
                if text_content:
                    self.markdown_content.append(f"   {text_content}")
                self.markdown_content.append("")
    
    def convert_xml_to_markdown(self, xml_file_path: str) -> str:
        """Convert XML file to markdown format."""
        try:
            # Parse the XML file
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            # Reset content
            self.markdown_content = []
            
            # Add document title
            self.markdown_content.append(f"# {root.tag.title()} Document")
            self.markdown_content.append("")
            
            # Add root attributes if any
            if root.attrib:
                self.markdown_content.append("## Document Attributes")
                self.markdown_content.append("")
                for key, value in root.attrib.items():
                    self.markdown_content.append(f"- **{key}**: {value}")
                self.markdown_content.append("")
            
            # Process root element content
            if root.text and root.text.strip():
                self.markdown_content.append("## Content")
                self.markdown_content.append("")
                self.markdown_content.append(self.clean_text(root.text))
                self.markdown_content.append("")
            
            # Process all child elements
            for child in root:
                self.process_element(child, 0)
            
            # Join all content with newlines
            return "\n".join(self.markdown_content)
            
        except ET.ParseError as e:
            return f"# XML Parse Error\n\nError parsing XML file: {e}\n"
        except FileNotFoundError:
            return f"# File Not Found\n\nThe file '{xml_file_path}' was not found.\n"
        except Exception as e:
            return f"# Conversion Error\n\nError converting XML to Markdown: {e}\n"


def create_table_from_elements(elements: List[ET.Element]) -> List[str]:
    """Create a markdown table from similar XML elements."""
    if not elements:
        return []

    # Get all unique attributes and child element names
    all_keys = set()
    for element in elements:
        all_keys.update(element.attrib.keys())
        all_keys.update([child.tag for child in element])

    if not all_keys:
        return []

    all_keys = sorted(list(all_keys))

    # Create table header
    table_lines = []
    header = "| " + " | ".join(all_keys) + " |"
    separator = "| " + " | ".join(["---"] * len(all_keys)) + " |"

    table_lines.append(header)
    table_lines.append(separator)

    # Create table rows
    for element in elements:
        row_values = []
        for key in all_keys:
            value = ""
            if key in element.attrib:
                value = element.attrib[key]
            else:
                child = element.find(key)
                if child is not None:
                    value = (child.text or "").strip()

            # Clean value for table cell
            value = value.replace("|", "\\|").replace("\n", " ")
            row_values.append(value)

        row = "| " + " | ".join(row_values) + " |"
        table_lines.append(row)

    return table_lines


def main():
    """Main function to convert XML to Markdown."""
    # File paths
    input_file = r"C:\Users\<USER>\Downloads\review.xml"
    output_file = r"C:\Users\<USER>\Downloads\review_markdown.md"

    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found.")
        print("Please make sure the file exists and the path is correct.")
        return

    # Create converter instance
    converter = XMLToMarkdownConverter()

    # Convert XML to Markdown
    print(f"Converting '{input_file}' to Markdown...")
    markdown_content = converter.convert_xml_to_markdown(input_file)

    # Write to output file
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        print(f"✅ Successfully converted XML to Markdown!")
        print(f"📄 Output saved to: {output_file}")
        print(f"📊 Generated {len(markdown_content.splitlines())} lines of markdown")

        # Show preview of first few lines
        lines = markdown_content.splitlines()
        print("\n📋 Preview (first 10 lines):")
        print("-" * 50)
        for i, line in enumerate(lines[:10], 1):
            print(f"{i:2d}: {line}")
        if len(lines) > 10:
            print(f"... and {len(lines) - 10} more lines")

    except Exception as e:
        print(f"❌ Error writing output file: {e}")


if __name__ == "__main__":
    main()
